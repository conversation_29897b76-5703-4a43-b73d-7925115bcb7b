# Fusion Ui

## Introduction

Fusion Ui is a Vue3 component library that is simple, elegant, and aesthetically pleasing

Due to its initial development and gradual improvement, it is not recommended for use in production environments

If you want to learn how to build the Vue3+Ts+Vite component library, it will be a good choice

## Features

- 🧜 Components Design - Provides neat & beautiful crafted UI components.
- 🎡 Introduce on demand - Provide resolver to automatically import only used components.
- 💪 TS Supported - Support TypeScript & type checked & type inference.

## Special Thanks

❤️ Thanks to [everyone](https://github.com/tsinghua-lau/fusion-ui/graphs/contributors)  who has already contributed to ```Fusion Ui```!

<a href="https://github.com/tsinghua-lau/fusion-ui/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=tsinghua-lau/fusion-ui" />
</a>


## Latest Version

[![release version](https://img.shields.io/github/v/release/tsinghua-lau/fusion-ui?display_name=tag)](https://www.npmjs.com/package/fusion-ui)

You can subscribe to this feed for new version notifications: https://github.com/tsinghua-lau/fusion-ui/releases

## Issue

If you find a bug, I hope you open an [issue](https://github.com/tsinghua-lau/fusion-ui/issues), Thanks.

## Join

If you are interested in this project, welcome to join us.

- [Code of Conduct](https://github.com/tsinghua-lau/fusion-ui/blob/master/CODE_OF_CONDUCT.md)
- [Contributing Guide](https://github.com/tsinghua-lau/fusion-ui/blob/master/CONTRIBUTING.md)
