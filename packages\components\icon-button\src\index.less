.fn-icon-button {
  background-color: transparent;
  appearance: none;
  padding: 8px;
  border-radius: 50%;
  height: fit-content;
  color: var(--fn-icon-button-color);
  &[disabled] {
    color: var(--fn-sys-color-disabled-level-0);
  }
  @media (any-hover: hover) {
    &:not([disabled]):hover {
      background-color: rgb(
        var(--fn-icon-button-color-rgb) /
          var(--md-sys-state-hover-state-layer-opacity)
      );
    }
  }
}
