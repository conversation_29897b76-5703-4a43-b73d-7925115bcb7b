*,
:before,
:after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  tab-size: 4;
  line-height: 1.5;
  font-feature-settings: normal;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  line-height: inherit;
}

a {
  text-decoration: none;
}

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

textarea {
  resize: vertical;
}

button,
select {
  text-transform: none;
}

button,
[type='reset'],
[type='submit'] {
  background-color: transparent;
  background-image: none;
  -webkit-appearance: button;
}

button,
[role='button'] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}

a,
input,
button,
textarea,
[class*='pl-'] {
  &:focus {
    outline: none;
  }
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
