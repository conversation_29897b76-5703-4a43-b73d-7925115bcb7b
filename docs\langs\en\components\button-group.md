---
title: Button Group
lang: en-US
---

<!-- <script setup lang="ts">
  import props from "../../../example/button/description/en-props.ts";
  import slots from "../../../example/button/description/en-slots.ts";
</script> -->

# Button Group

The ButtonGroup COmponent can be used to group the button.

## Usage

### Basic button

The `Button` components can be grouped by the `ButtonGroup` as its immediate children.

You can set all the button props via `ButtonGroup` instead of set through the `Button` one by one.

<demo src="../../../example/button-group/basic.vue" />

### Variants

All the standard button variants are supported.

<demo src="../../../example/button-group/variant.vue" />

### Orientation

`ButtonGroup` support horizontal (defalut) and vertical

<demo src="../../../example/button-group/orientation.vue" />

### Size and Color

The `size` and `color` props can be used to control the appearance of the button group.

<demo src="../../../example/button-group/size-color.vue" />
