.fn-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  border-radius: var(--md-sys-shape-corner-extra-small-default-size);
  padding: 6px 16px;
  color: var(--fn-alert-on-color);

  .fn-alert--message {
    flex: 1;
    padding: 8px 0;
  }

  .fn-alert--icon,
  > .fn-icon {
    font-size: 24px;
    color: var(--fn-alert-highlight-color);
    // margin: 6px 0 auto;
  }
}

// variants
.fn-alert {
  &--filled {
    background-color: var(--fn-alert-color);
  }

  &--outlined {
    border: 1px solid var(--fn-alert-highlight-color);
    background: transparent;
  }
}
