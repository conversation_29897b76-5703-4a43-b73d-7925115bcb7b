{"extends": "./tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "allowJs": true, "baseUrl": "./", "emitDeclarationOnly": true, "declaration": true, "declarationDir": "./packages/fusion-ui/dist/types", "types": ["node"], "paths": {"@fusion-ui-vue/components": ["packages/components", "@fusion-ui-vue/components"], "@fusion-ui-vue/constants": ["packages/constants", "@fusion-ui-vue/constants"], "@fusion-ui-vue/hooks": ["packages/hooks", "@fusion-ui-vue/hooks"], "@fusion-ui-vue/theme": ["packages/theme", "@fusion-ui-vue/theme"], "@fusion-ui-vue/utils": ["packages/utils", "@fusion-ui-vue/utils"]}}, "include": ["packages/**/*", "packages/components/typings.d.ts", "packages/components/shims-vue.d.ts"], "exclude": ["node_modules", "build", "dist", "test", "docs", "playground", "gulpfile.ts", "**__/test/__**/*", "**/style/**/*", "**/*.md"]}