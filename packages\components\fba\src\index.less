.fn-fba {
  background-color: var(--fn-fba-color);
  color: var(--fn-fba-on-color);
  height: fit-content;
  width: fit-content;
  box-shadow: var(--md-sys-elevation-level-5);
  gap: 8px;
  text-transform: none;
  z-index: var(--fn-sys-z-index-fab);
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
    box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

  @media (any-hover: hover) {
    &:hover {
      box-shadow: var(--md-sys-elevation-level-8);
      background-color: color-mix(
        in srgb,
        var(--fn-fba-color),
        var(--fn-sys-color-switch-reverse)
          var(--md-sys-state-hover-state-layer-opacity-percentage)
      );
    }
  }
}

// size
.fn-fba {
  &--small {
    border-radius: var(--md-sys-shape-corner-medium-default-size);
    padding: 8px;
    .fn-icon {
      font-size: 24px;
    }
  }
  &--medium {
    border-radius: var(--md-sys-shape-corner-large-default-size);
    padding: 16px;
    .fn-icon {
      font-size: 24px;
    }
  }
  &--large {
    border-radius: var(--md-sys-shape-corner-extra-large-default-size);
    padding: 30px;
    .fn-icon {
      font-size: 36px;
    }
  }
}
