<script lang="ts" setup name="demo-block">
import { computed } from 'vue'
import { useClipboard, useToggle } from '@vueuse/core'
import { demoProps } from './index'

const props = defineProps(demoProps)

const decodedHighlightedCode = computed(() =>
  decodeURIComponent(props.highlightedCode)
)

const { copy, copied } = useClipboard({
  source: decodeURIComponent(props.code),
})
const [value, toggle] = useToggle()
</script>

<template>
  <ClientOnly>
    <div v-bind="$attrs" class="mt-6 demo-block">
      <div class="o-demo_wrapper vp-raw bg">
        <slot />
      </div>
      <div class="relative">
        <div class="o-demo_actions">
          <a
            class="o-demo_action_item"
            group
            :href="codeSandBox"
            target="_blank"
          >
            <div class="o-demo_action_icon i-carbon:chemistry" />
            <div class="o-demo_tooltip" group-hover:opacity-100>
              Open on Playground(WIP)
            </div>
          </a>
          <a class="o-demo_action_item" group :href="github" target="_blank">
            <div class="o-demo_action_icon i-carbon-logo-github" />
            <div class="o-demo_tooltip" group-hover:opacity-100>
              Edit on GitHub
            </div>
          </a>
          <button class="o-demo_action_item" group @click="copy()">
            <div class="o-demo_action_icon i-carbon:copy" />
            <div class="o-demo_tooltip" group-hover:opacity-100>
              {{ copied ? 'Copied' : 'Copy code' }}
            </div>
          </button>
          <button class="o-demo_action_item" group @click="toggle()">
            <div class="o-demo_action_icon i-carbon:fit-to-width" />
            <div class="o-demo_tooltip" group-hover:opacity-100>
              {{ value ? 'Hidden code' : 'Expand code' }}
            </div>
          </button>
        </div>
        <div
          v-if="$props.highlightedPreviewCode"
          v-show="!value"
          :class="`language-${lang} extra-class`"
          v-html="decodeURIComponent($props.highlightedPreviewCode)"
        />
        <div
          v-show="value"
          :class="`language-${lang} extra-class`"
          v-html="decodedHighlightedCode"
        />
      </div>
    </div>
  </ClientOnly>
</template>
