{"name": "fusion-ui-vue", "version": "1.0.0", "packageManager": "pnpm@7.5.2", "description": "A Component Library for Vue 3", "author": "tsinghua-lau", "license": "MIT", "homepage": "https://tsinghua-lau.github.io/fusion-ui/", "repository": {"type": "git", "url": "git+https://github.com/tsinghua-lau/fusion-ui.git"}, "keywords": ["fusion-ui-vue", "vue", "vue3", "component", "library"], "sideEffects": ["dist/**/*.css", "*.css"], "main": "dist/lib/components/index.js", "module": "dist/es/components/index.mjs", "style": "dist/styles/index.css", "types": "dist/types/packages/components/index.d.ts", "files": ["dist"], "dependencies": {"@fusion-ui-vue/components": "^0.0.2", "@fusion-ui-vue/constants": "^0.0.2", "@fusion-ui-vue/hooks": "^0.0.2", "@fusion-ui-vue/theme": "^0.0.2", "@fusion-ui-vue/utils": "^0.0.2", "@pnpm/types": "github:pnpm/types", "@vue/shared": "^3.2.47", "@vueuse/core": "^9.1.1", "lodash-unified": "^1.0.3", "vite-plugin-progress": "^0.0.7", "vue": "^3.3.8"}}