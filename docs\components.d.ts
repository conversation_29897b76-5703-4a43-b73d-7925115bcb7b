/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    FnButton: typeof import('fusion-ui-vue/components/index')['FnButton']
    FnInput: typeof import('fusion-ui-vue/components/index')['FnInput']
    FnDialog: typeof import('fusion-ui-vue/components/index')['FnDialog']
    FnRadio: typeof import('fusion-ui-vue/components/index')['FnRadio']
  }
}
