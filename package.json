{"name": "fusion-ui", "version": "1.0.0", "packageManager": "pnpm@7.5.2", "license": "MIT", "keywords": ["fusion-ui-vue", "vue", "vue3", "component", "library"], "workspaces": ["packages/*", "example", "docs"], "scripts": {"docs:dev": "pnpm -C docs run dev", "docs:preview": "pnpm -C docs run preview", "play:dev": "pnpm -C playground run dev", "docs:build": "pnpm run build && pnpm -C docs run build", "build": "rimraf ./packages/fusion-ui/dist && gulp --require @esbuild-kit/cjs-loader -f gulpfile.ts && vue-tsc", "test": "vitest", "deploy": "pnpm run docs:build", "lint": "eslint .", "lint:fix": "eslint . --fix", "release": "npx esno ./scripts/release.ts", "prepare": "husky install"}, "dependencies": {"@fusion-ui-vue/components": "workspace:*", "@fusion-ui-vue/constants": "workspace:*", "@fusion-ui-vue/hooks": "workspace:*", "@fusion-ui-vue/theme": "workspace:*", "@fusion-ui-vue/utils": "workspace:*", "@pnpm/types": "github:pnpm/types", "@vue/shared": "^3.2.47", "@vueuse/core": "^9.1.1", "fusion-ui-iconify": "1.0.46", "fusion-ui-vue": "workspace:*", "lodash-unified": "^1.0.3", "vue": "^3.3.8"}, "devDependencies": {"@antfu/eslint-config": "^0.35.3", "@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@commitlint/types": "^17.4.4", "@ctrl/tinycolor": "^3.5.0", "@esbuild-kit/cjs-loader": "^2.4.4", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@types/fs-extra": "^9.0.13", "@types/gulp": "^4.0.10", "@types/node": "^18.11.18", "@vitejs/plugin-vue": "^4.4.1", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/compiler-core": "^3.2.47", "@vue/test-utils": "^2.2.10", "chalk": "^5.2.0", "consola": "^2.15.3", "eslint": "^8.18.0", "esno": "^0.16.3", "execa": "^6.1.0", "fast-glob": "^3.2.12", "fs-extra": "^11.1.0", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-import-less": "^0.9.0", "gulp-less": "^5.0.0", "gulp-rename": "^2.0.0", "husky": "^8.0.1", "jsdom": "^21.1.0", "less": "^4.1.3", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^4.4.1", "rollup-plugin-esbuild": "^6.1.0", "sucrase": "^3.34.0", "typescript": "^4.9.3", "unocss": "^0.50.0", "unplugin-auto-import": "^0.15.0", "unplugin-vue-components": "^0.24.0", "vite": "^4.5.0", "vitepress": "1.0.0-rc.25", "vitest": "^0.28.5", "vue-router": "4", "vue-tsc": "^1.8.22"}, "lint-staged": {"*.{vue,js,ts,jsx,tsx,md,json}": "eslint --fix"}}