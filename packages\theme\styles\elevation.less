/*
 Copyright 2016 Google Inc. All rights reserved.

 Licensed under the Apache License, Version 2.0 (the License);
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at


   http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an AS IS BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/

:root {
  --md-sys-elevation-level-0: none;
  --md-sys-elevation-level-1: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-2: 0px 3px 1px -2px rgba(0, 0, 0, 0.2),
    0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-3: 0px 3px 3px -2px rgba(0, 0, 0, 0.2),
    0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-4: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
    0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-5: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
    0px 5px 8px 0px rgba(0, 0, 0, 0.14), 0px 1px 14px 0px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-6: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
    0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-7: 0px 4px 5px -2px rgba(0, 0, 0, 0.2),
    0px 7px 10px 1px rgba(0, 0, 0, 0.14), 0px 2px 16px 1px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-8: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
    0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-9: 0px 5px 6px -3px rgba(0, 0, 0, 0.2),
    0px 9px 12px 1px rgba(0, 0, 0, 0.14), 0px 3px 16px 2px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-10: 0px 6px 6px -3px rgba(0, 0, 0, 0.2),
    0px 10px 14px 1px rgba(0, 0, 0, 0.14), 0px 4px 18px 3px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-11: 0px 6px 7px -4px rgba(0, 0, 0, 0.2),
    0px 11px 15px 1px rgba(0, 0, 0, 0.14), 0px 4px 20px 3px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-12: 0px 7px 8px -4px rgba(0, 0, 0, 0.2),
    0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-13: 0px 7px 8px -4px rgba(0, 0, 0, 0.2),
    0px 13px 19px 2px rgba(0, 0, 0, 0.14), 0px 5px 24px 4px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-14: 0px 7px 9px -4px rgba(0, 0, 0, 0.2),
    0px 14px 21px 2px rgba(0, 0, 0, 0.14), 0px 5px 26px 4px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-15: 0px 8px 9px -5px rgba(0, 0, 0, 0.2),
    0px 15px 22px 2px rgba(0, 0, 0, 0.14), 0px 6px 28px 5px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-16: 0px 8px 10px -5px rgba(0, 0, 0, 0.2),
    0px 16px 24px 2px rgba(0, 0, 0, 0.14), 0px 6px 30px 5px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-17: 0px 8px 11px -5px rgba(0, 0, 0, 0.2),
    0px 17px 26px 2px rgba(0, 0, 0, 0.14), 0px 6px 32px 5px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-18: 0px 9px 11px -5px rgba(0, 0, 0, 0.2),
    0px 18px 28px 2px rgba(0, 0, 0, 0.14), 0px 7px 34px 6px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-19: 0px 9px 12px -6px rgba(0, 0, 0, 0.2),
    0px 19px 29px 2px rgba(0, 0, 0, 0.14), 0px 7px 36px 6px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-20: 0px 10px 13px -6px rgba(0, 0, 0, 0.2),
    0px 20px 31px 3px rgba(0, 0, 0, 0.14), 0px 8px 38px 7px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-21: 0px 10px 13px -6px rgba(0, 0, 0, 0.2),
    0px 21px 33px 3px rgba(0, 0, 0, 0.14), 0px 8px 40px 7px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-22: 0px 10px 14px -6px rgba(0, 0, 0, 0.2),
    0px 22px 35px 3px rgba(0, 0, 0, 0.14), 0px 8px 42px 7px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-23: 0px 11px 14px -7px rgba(0, 0, 0, 0.2),
    0px 23px 36px 3px rgba(0, 0, 0, 0.14), 0px 9px 44px 8px rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level-24: 0px 11px 15px -7px rgba(0, 0, 0, 0.2),
    0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12);
}

.elevation-0 {
  box-shadow: var(--md-sys-elevation-level-0);
}
.elevation-1 {
  box-shadow: var(--md-sys-elevation-level-1);
}
.elevation-2 {
  box-shadow: var(--md-sys-elevation-level-2);
}
.elevation-3 {
  box-shadow: var(--md-sys-elevation-level-3);
}
.elevation-4 {
  box-shadow: var(--md-sys-elevation-level-4);
}
.elevation-5 {
  box-shadow: var(--md-sys-elevation-level-5);
}
.elevation-6 {
  box-shadow: var(--md-sys-elevation-level-6);
}
.elevation-7 {
  box-shadow: var(--md-sys-elevation-level-7);
}
.elevation-8 {
  box-shadow: var(--md-sys-elevation-level-8);
}
.elevation-9 {
  box-shadow: var(--md-sys-elevation-level-9);
}
.elevation-10 {
  box-shadow: var(--md-sys-elevation-level-10);
}
.elevation-11 {
  box-shadow: var(--md-sys-elevation-level-11);
}
.elevation-12 {
  box-shadow: var(--md-sys-elevation-level-12);
}
.elevation-13 {
  box-shadow: var(--md-sys-elevation-level-13);
}
.elevation-14 {
  box-shadow: var(--md-sys-elevation-level-14);
}
.elevation-15 {
  box-shadow: var(--md-sys-elevation-level-15);
}
.elevation-16 {
  box-shadow: var(--md-sys-elevation-level-16);
}
.elevation-17 {
  box-shadow: var(--md-sys-elevation-level-17);
}
.elevation-18 {
  box-shadow: var(--md-sys-elevation-level-18);
}
.elevation-19 {
  box-shadow: var(--md-sys-elevation-level-19);
}
.elevation-20 {
  box-shadow: var(--md-sys-elevation-level-20);
}
.elevation-21 {
  box-shadow: var(--md-sys-elevation-level-21);
}
.elevation-22 {
  box-shadow: var(--md-sys-elevation-level-22);
}
.elevation-23 {
  box-shadow: var(--md-sys-elevation-level-23);
}
.elevation-24 {
  box-shadow: var(--md-sys-elevation-level-24);
}
