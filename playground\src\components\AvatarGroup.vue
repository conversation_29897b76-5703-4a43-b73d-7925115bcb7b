<script lang="ts" setup>
import { FnAvatar, FnAvatarGroup } from '@fusion-ui-vue/components'
</script>

<template>
  <fn-avatar-group max="3" size="50">
    <fn-avatar
      alt="<PERSON><PERSON>"
      src="https://mui.com/static/images/avatar/1.jpg"
    />
    <fn-avatar
      alt="<PERSON>"
      src="https://mui.com/static/images/avatar/2.jpg"
      size="56"
    />
    <fn-avatar
      alt="<PERSON>"
      src="https://mui.com/static/images/avatar/3.jpg"
    />
    <fn-avatar
      alt="<PERSON>"
      src="https://mui.com/static/images/avatar/4.jpg"
    />
    <fn-avatar
      alt="<PERSON>"
      src="https://mui.com/static/images/avatar/5.jpg"
    />
  </fn-avatar-group>
</template>
