.is-disabled,
.is-disabled-link {
  opacity: 0.8;
  cursor: not-allowed !important;
  text-decoration: none !important;
  &:hover {
    cursor: not-allowed !important;
    background-image: none !important;
    color: var(--fn-button-disabled-text-color) !important;
    border-color: var(--fn-button-disabled-border-color) !important;
    background-color: var(--fn-button-disabled-bg-color) !important;
  }
}
.is-disabled-link {
  &:hover {
    color: var(--fn-link-disabled-text-color) !important;
  }
}
.is-underline {
  &:hover {
    text-decoration: none !important;
  }
}
.fn-rippleBase-root {
  position: relative;
}
.is-hidden {
  // display: none;
}
