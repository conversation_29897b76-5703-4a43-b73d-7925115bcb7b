export const enComponents = [
  {
    text: 'Basic',
    items: [
      {
        text: 'Button',
        link: '/langs/en/components/button',
      },
      {
        text: 'ButtonGroup',
        link: '/langs/en/components/button-group',
      },
      {
        text: 'FBA',
        link: '/langs/en/components/fba',
      },
      {
        text: 'Icons',
        link: '/langs/en/components/icon',
      },
      {
        text: 'Link',
        link: '/langs/en/components/link',
      },
      {
        text: 'Text Field',
        link: '/langs/en/components/text-field',
      },
      {
        text: 'Checkbox',
        link: '/langs/en/components/checkbox',
      },
      {
        text: 'Radio',
        link: '/langs/en/components/radio',
      },
    ],
  },
  {
    text: 'Data',
    items: [
      {
        text: 'Avatar',
        link: '/langs/en/components/avatar',
      },
      {
        text: 'Badge',
        link: '/langs/en/components/badge',
      },
      {
        text: 'Card',
        link: '/langs/en/components/card',
      },
      {
        text: 'Tag',
        link: '/langs/en/components/tag',
      },
      {
        text: 'List',
        link: '/langs/en/components/list',
      },
      {
        text: 'Typography',
        link: '/langs/en/components/typography',
      },
      {
        text: 'Divider',
        link: '/langs/en/components/divider',
      },
    ],
  },
  {
    text: 'Feedback',
    items: [
      {
        text: 'Alert',
        link: '/langs/en/components/alert',
      },
      {
        text: 'Message',
        link: '/langs/en/components/message',
      },
      {
        text: 'Dialog',
        link: '/langs/en/components/dialog',
      },
      {
        text: 'Switch',
        link: '/langs/en/components/switch',
      },
      {
        text: 'SwitchNew',
        link: '/langs/en/components/switch-new',
      },
      {
        text: 'Progress',
        link: '/langs/en/components/progress',
      },
    ],
  },
  {
    text: 'Navigation',
    items: [
      {
        text: 'Drawer',
        link: '/langs/en/components/drawer',
      },
      {
        text: 'Breadcrumb',
        link: '/langs/en/components/breadcrumb',
      },
      {
        text: 'Menu',
        link: '/langs/en/components/menu',
      },
    ],
  },
  {
    text: 'Utils',
    items: [
      {
        text: 'Popover',
        link: '/langs/en/components/popover',
      },
      {
        text: 'Collapse',
        link: '/langs/en/components/collapse',
      },
      {
        text: 'Modal',
        link: '/langs/en/components/modal',
      },
    ],
  },
]

export const zhComponents = [
  {
    text: '基础组件',
    items: [
      {
        text: 'Button 按钮',
        link: '/langs/zh/components/button',
      },
      {
        text: 'ButtonGroup 按钮组',
        link: '/langs/zh/components/button-group',
      },
      {
        text: 'FBA 悬浮按钮',
        link: '/langs/zh/components/fba',
      },
      {
        text: 'Icons 图标',
        link: '/langs/zh/components/icon',
      },
      {
        text: 'Link 链接',
        link: '/langs/zh/components/link',
      },
      {
        text: 'Text Field 文本输入',
        link: '/langs/zh/components/text-field',
      },
      {
        text: 'Checkbox 多选框',
        link: '/langs/zh/components/checkbox',
      },
      {
        text: 'Radio 单选框',
        link: '/langs/zh/components/radio',
      },
    ],
  },
  {
    text: 'Data 数据展示',
    items: [
      {
        text: 'Avatar 头像',
        link: '/langs/zh/components/avatar',
      },
      {
        text: 'Badge 徽章',
        link: '/langs/zh/components/badge',
      },
      {
        text: 'Card 卡片',
        link: '/langs/zh/components/card',
      },
      {
        text: 'Progress 进度条',
        link: '/langs/zh/components/progress',
      },
      {
        text: 'Tag 标签',
        link: '/langs/zh/components/tag',
      },
      {
        text: 'List 列表',
        link: '/langs/zh/components/list',
      },
      {
        text: 'Typography 板式',
        link: '/langs/zh/components/typography',
      },
      {
        text: 'Divider 分割线',
        link: '/langs/zh/components/divider',
      },
    ],
  },
  {
    text: 'Feedback 反馈组件',
    items: [
      {
        text: 'Alert 提示',
        link: '/langs/zh/components/alert',
      },
      {
        text: 'Message 消息',
        link: '/langs/zh/components/message',
      },
      {
        text: 'Dialog 对话框',
        link: '/langs/zh/components/dialog',
      },
      {
        text: 'Switch 开关',
        link: '/langs/zh/components/switch',
      },
      {
        text: 'Switch new 开关',
        link: '/langs/zh/components/switch-new',
      },
    ],
  },
  {
    text: 'Navigation 导航',
    items: [
      {
        text: 'Drawer 抽屉',
        link: '/langs/zh/components/drawer',
      },
      {
        text: 'Breadcrumb 面包屑',
        link: '/langs/zh/components/breadcrumb',
      },
      {
        text: 'Menu 菜单',
        link: '/langs/zh/components/menu',
      },
    ],
  },
  {
    text: 'Utils 工具',
    items: [
      {
        text: 'Popover 弹出框',
        link: '/langs/zh/components/popover',
      },
      {
        text: 'Collapse 折叠面板',
        link: '/langs/zh/components/collapse',
      },
      {
        text: 'Modal 蒙板',
        link: '/langs/zh/components/modal',
      },
    ],
  },
]
