@medium-size: {
  switchWidth: 58px;
  switchHeight: 38px;
  overlaySize: 38px;
  dotSize: 20px;
  padding:13px;
};

@small-size: {
  switchWidth: 58px;
  switchHeight: 38px;
  overlaySize: 38px;
  dotSize: 16px;
  padding:14px;
};

.fn-switch-new {
  position: relative;
  z-index: 0;
  display: inline-flex;
  overflow: hidden;
  box-sizing: border-box;

  &__overlay--medium,
  &__overlay--small{
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    border-radius: 50%;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__dot--medium,
  &__dot--small{
    z-index: 1;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: left 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
      transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    background-color: var(--fn-switch--dot-color);
    box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
      0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  }

  &__overlay--medium{
    width:  @medium-size[overlaySize];
    height: @medium-size[overlaySize];
  }

  &__overlay--small{
    width:  @small-size[overlaySize];
    height: @small-size[overlaySize];
  }

  &__dot--medium{
      width:  @medium-size[dotSize];
      height: @medium-size[dotSize];
  }

  &__dot--small{
      width:  @small-size[dotSize];
      height: @small-size[dotSize];
  }

  &--medium{
      width: @medium-size[switchWidth];
      height: @medium-size[switchHeight] ;
      padding:@medium-size[padding] ;
  }

  &--small{
      width: @small-size[switchWidth] ;
      height: @small-size[switchHeight] ;
      padding:@small-size[padding] ;
  }

  &__input {
    position: absolute;
    cursor: pointer;
    width: 200%;
    height: 100%;
    left: -50%;
    top: 0;
    z-index: 1;
    opacity: 0;
    @media (any-hover: hover) {
      &:not([disabled]):hover {
        cursor: pointer;
        background-color: color-mix(
          in srgb,
          var(--fn-switch--dot-hover-color)
            var(--md-sys-state-hover-state-layer-opacity-percentage),
          var(--fn-sys-color-switch) var(--fn-zero-percentage)
        );
      }
      &:is([disabled]) {
        cursor: default;
      }
    }
  }

  &--enabled {
    &:hover {
      background-color: color-mix(
        in srgb,
        var(--fn-switch--dot-hover-color)
          var(--md-sys-state-hover-state-layer-opacity-percentage),
        var(--fn-sys-color-switch) var(--fn-zero-percentage)
      );
    }
  }

  &--active {
    transform: translateX(20px);
  }

  &__track {
    width: 100%;
    height: 100%;
    border-radius: 7px;
    background-color: var(--fn-switch--track-color);
    opacity: 0.5;
    z-index: -1;
    transition: all 0.2s ease-in-out;
  }
}

