> English | [简体中文](./CONTRIBUTING.zh-CN.md)

# Contributing

One person runs fast, and a group of people run farther. We welcome contributions in any form, including but not limited to the following:

* Problem suggestions
* Improve documentation
* Perfect examples
* Improve testing
* Improve components
* Submit PR
* Participate in discussions
* Share project
* ...


🍉🍉🍉🍉 View [Component development process!](https://github.com/tsinghua-lau/fusion-ui/tree/master/playground#readme)

🍉🍉🍉🍉 View [Component development process!](https://github.com/tsinghua-lau/fusion-ui/tree/master/playground#readme)

🍉🍉🍉🍉 View [Component development process!](https://github.com/tsinghua-lau/fusion-ui/tree/master/playground#readme)


## Submit Pull Request

1. Fork [This warehouse](https://github.com/tsinghua-lau/fusion-ui)
2. Enter the local project root directory and use ```pnpm i``` to install dependencies.
3.Use ```pnpm run docs:dev``` to start the project and view the document.
4. Please pull the latest code before submitting to avoid file conflicts.
5. Submit ```git commit``, please abide by it at the same time ``````, Please abide by it at the same time. [Commit Standard](#Commit-指南)。
6. Submit Pull Request。

### Commit types

The following is a list of commit types:

- feat: A new feature or functionality
- fix: A bug fix
- docs: Documentation only changes
- style: Code formatting or component style changes
- refactor: Code changes that neither fixes a bug nor adds a feature.
- perf: Improve performance.
- test: Add missing or correct existing tests.
- chore: Other commits that don’t modify src or test files.

## License

By contributing your code to the repository, you agree to license your contribution under the [MIT license](./LICENSE).
