# DemoBlock

## Insert code snippets in markdown,

```markdown
<demo src="./example/helloWorld.vue">
```

![](https://raw.githubusercontent.com/imageList/imglist/master/img/20220907175225.png)

## Specify Github and CodeSandBox link

```markdown
<demo src="./example/helloWorld.vue" github="https://github.com/tsinghua-lau/fusion-ui" codeSandBox="https://github.com/tsinghua-lau/fusion-ui">
```

## Specify language

```markdown
<demo src="./example/helloWorld.vue" github="https://github.com/tsinghua-lau/fusion-ui" codeSandBox="https://github.com/tsinghua-lau/fusion-ui" lang="ts">
```

## Expand default

```markdown
<demo src="./example/helloWorld.vue" github="https://github.com/tsinghua-lau/fusion-ui" codeSandBox="https://github.com/tsinghua-lau/fusion-ui" lang="ts" expand>
```

![](https://raw.githubusercontent.com/imageList/imglist/master/img/20220907175651.png)
