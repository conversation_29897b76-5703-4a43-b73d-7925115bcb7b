name: Deploy

on:
  push:
    branches:
      - main  # 如果你的默认分支是 main
      - master  # 保留 master 以防万一

# 添加权限声明
permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install pnpm
        run: npm i -g pnpm
      - name: install & Build
        run: pnpm i && pnpm docs:build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          # 使用 GITHUB_TOKEN（推荐）
          github_token: ${{ secrets.GITHUB_TOKEN }}
          # 如果上面不行，可以创建个人访问令牌并使用：
          # personal_token: ${{ secrets.PERSONAL_TOKEN }}
          publish_dir: docs/.vitepress/dist
          # 强制推送到 gh-pages 分支
          force_orphan: true
