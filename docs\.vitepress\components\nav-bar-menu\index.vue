<script lang="ts" setup>
import { useData } from 'vitepress/dist/client/theme-default/composables/data.js'
import VPNavBarMenuLink from 'vitepress/dist/client/theme-default/components/VPNavBarMenuLink.vue'
import VPNavBarMenuGroup from 'vitepress/dist/client/theme-default/components/VPNavBarMenuGroup.vue'
import { computed } from 'vue'

const { theme, lang } = useData()

const langNav = computed(() => theme.value.nav[lang.value])
</script>

<template>
  <nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu">
    <span id="main-nav-aria-label" class="visually-hidden">Main Navigation</span>
    <template v-for="item in langNav" :key="item.text">
      <v-p-nav-bar-menu-link v-if="'link' in item" :item="item" />
      <v-p-nav-bar-menu-group v-else :item="item" />
    </template>
  </nav>
</template>

<style scoped>
.VPNavBarMenu {
  display: none;
}

@media (min-width: 768px) {
  .VPNavBarMenu {
    display: flex;
  }
}
</style>
