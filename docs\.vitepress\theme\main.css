/**
 * Customize default theme styling by overriding CSS variables:
 * https://github.com/vuejs/vitepress/blob/main/src/client/theme-default/styles/vars.css
 */

/**
 * Colors
 * -------------------------------------------------------------------------- */

:root {
  --vp-c-brand: #6750a4;
  --vp-c-brand-light: #7d65bb;
  --vp-c-brand-lighter: #9880d8;
  --vp-c-brand-lightest: #b49bf5;
  --vp-c-brand-dark: #4d3489;
  --vp-c-brand-darker: #361a70;
  --vp-c-brand-dimm: rgba(103, 80, 164, 0.08);
  --vp-c-brand-1: var(--vp-c-brand);
  --vp-c-brand-2: var(--vp-c-brand-light);
  --vp-c-brand-3: var(--vp-c-brand-lighter);

  --vp-code-block-bg: rgba(125, 125, 125, 0.04);
  --n-code-border-radius: 2px;
  --n-code-text-color: rgb(51, 54, 57);
  --n-code-color: rgb(244, 244, 248);
  --n-code-border: 1px solid #0000;
}

:root[data-theme='dark'] {
  --vp-c-brand: #cebbfb;
  --vp-c-brand-light: #cebbfb;
  --vp-c-brand-lighter: #cebbfb;
  --vp-c-brand-lightest: #cebbfb;
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: var(--vp-c-brand-light);
  --vp-button-brand-text: var(--vp-c-white);
  --vp-button-brand-bg: var(--vp-c-brand);
  --vp-button-brand-hover-border: var(--vp-c-brand-light);
  --vp-button-brand-hover-text: var(--vp-c-white);
  --vp-button-brand-hover-bg: var(--vp-c-brand-light);
  --vp-button-brand-active-border: var(--vp-c-brand-light);
  --vp-button-brand-active-text: var(--vp-c-white);
  --vp-button-brand-active-bg: var(--vp-button-brand-bg);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #bd34fe 30%,
    #41d1ff
  );
  --vp-home-hero-image-background-image: linear-gradient(
    135deg,
    #5151e5 10%,
    #72edf2 100%
  );
  --vp-home-hero-image-filter: blur(150px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }
}

/**
 * Component: Custom Block
 * -------------------------------------------------------------------------- */

:root {
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-text: var(--vp-c-brand-darker);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

.dark {
  --vp-custom-block-tip-border: var(--vp-c-brand);
  --vp-custom-block-tip-text: var(--vp-c-brand-lightest);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
  --vp-code-block-bg: rgba(0, 0, 0, 0.2);
  --vp-c-text-code: #c0cec0;
  --n-code-border-radius: 2px;
  --n-code-text-color: rgba(255, 255, 255, 0.82);
  --n-code-color: rgba(255, 255, 255, 0.12);
  --n-code-border: 1px solid #0000;
}

/**
 * Component: Algolia
 * -------------------------------------------------------------------------- */

.DocSearch {
  --docsearch-primary-color: var(--vp-c-brand) !important;
}

/**
 * VitePress: Custom fix
 * -------------------------------------------------------------------------- */

/*
  Use lighter colors for links in dark mode for a11y.
  Also specify some classes twice to have higher specificity
  over scoped class data attribute.
*/
.dark .vp-doc a:not(.fn-link),
.dark .vp-doc a > code,
.dark .VPNavBarMenuLink.VPNavBarMenuLink:hover,
.dark .VPNavBarMenuLink.VPNavBarMenuLink.active,
.dark .link.link:hover,
.dark .link.link.active,
.dark .edit-link-button.edit-link-button,
.dark .pager-link .title {
  color: var(--vp-c-brand-lighter);
}

.dark .vp-doc a:not(.fn-link):hover,
.dark .vp-doc a > code:hover {
  color: var(--vp-c-brand-lightest);
  opacity: 1;
}

.vp-doc table {
  margin: 0 !important;
  overflow-x: inherit;
}

/* Transition by color instead of opacity */
.dark .vp-doc .custom-block a {
  transition: color 0.25s;
}

.VPContent {
  background-color: var(--vp-c-bg);
}

.o-demo_wrapper {
  background-color: var(--vp-c-bg);
  flex-wrap: wrap;
}

.demo-block div[class*='language-'] + div[class*='language-'] {
  margin-top: 16px;
}
