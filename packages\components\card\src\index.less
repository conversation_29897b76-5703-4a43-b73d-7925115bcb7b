.fn-card {
  min-width: 275px;
  border-radius: var(--md-sys-shape-corner-medium-default-size);
  overflow: hidden;
  width: fit-content;
  height: fit-content;
}

// variant
.fn-card {
  &--elevated {
    background-color: var(--md-sys-color-surface-container-low);
    box-shadow: var(--md-sys-elevation-level-1);
  }
  &--filled {
    background-color: var(--md-sys-color-surface-container-highest);
  }
  &--outlined {
    background-color: var(--md-sys-color-surface);
    border: 1px solid var(--md-sys-color-outline-variant);
  }
}
