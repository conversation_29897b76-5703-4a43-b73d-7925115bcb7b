/*
 Copyright 2016 Google Inc. All rights reserved.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/

.primary {
  color: var(--md-sys-color-on-primary);
  background-color: var(--md-sys-color-primary);
}
.on-primary {
  color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-on-primary);
}
.primary-container {
  color: var(--md-sys-color-on-primary-container);
  background-color: var(--md-sys-color-primary-container);
}
.on-primary-container {
  color: var(--md-sys-color-primary-container);
  background-color: var(--md-sys-color-on-primary-container);
}
.secondary {
  color: var(--md-sys-color-on-secondary);
  background-color: var(--md-sys-color-secondary);
}
.on-secondary {
  color: var(--md-sys-color-secondary);
  background-color: var(--md-sys-color-on-secondary);
}
.secondary-container {
  color: var(--md-sys-color-on-secondary-container);
  background-color: var(--md-sys-color-secondary-container);
}
.on-secondary-container {
  color: var(--md-sys-color-secondary-container);
  background-color: var(--md-sys-color-on-secondary-container);
}
.tertiary {
  color: var(--md-sys-color-on-tertiary);
  background-color: var(--md-sys-color-tertiary);
}
.on-tertiary {
  color: var(--md-sys-color-tertiary);
  background-color: var(--md-sys-color-on-tertiary);
}
.tertiary-container {
  color: var(--md-sys-color-on-tertiary-container);
  background-color: var(--md-sys-color-tertiary-container);
}
.on-tertiary-container {
  color: var(--md-sys-color-tertiary-container);
  background-color: var(--md-sys-color-on-tertiary-container);
}
.background {
  color: var(--md-sys-color-on-background);
  background-color: var(--md-sys-color-background);
}
.surface {
  color: var(--md-sys-color-on-surface);
  background-color: var(--md-sys-color-surface);
}
.surface-variant {
  color: var(--md-sys-color-on-surface-variant);
  background-color: var(--md-sys-color-surface-variant);
}
.on-surface-variant {
  color: var(--md-sys-color-surface-variant);
  background-color: var(--md-sys-color-on-surface-variant);
}
.outline {
  border: 1px solid var(--md-sys-color-outline);
}
.inverse-surface {
  color: var(--md-sys-color-on-inverse-surface);
  background-color: var(--md-sys-color-inverse-surface);
}
.on-inverse-surface {
  color: var(--md-sys-color-inverse-surface);
  background-color: var(--md-sys-color-on-inverse-surface);
}
.inverse-primary {
  color: var(--md-sys-color-on-inverse-primary);
  background-color: var(--md-sys-color-inverse-primary);
}
.on-inverse-primary {
  color: var(--md-sys-color-inverse-primary);
  background-color: var(--md-sys-color-on-inverse-primary);
}
.surface-tint {
  background-color: var(--md-sys-color-on-surface-tint);
}
.black {
  background-color: var(--md-ref-palette-black);
}
.black-text {
  color: var(--md-ref-palette-black);
}
.white {
  background-color: var(--md-ref-palette-white);
}
.white-text {
  color: var(--md-ref-palette-white);
}
.error {
  color: var(--md-sys-color-on-error);
  background-color: var(--md-sys-color-error);
}
.on-error {
  color: var(--md-sys-color-error);
  background-color: var(--md-sys-color-on-error);
}
.error-container {
  color: var(--md-sys-color-on-error-container) !important;
  background-color: var(--md-sys-color-error-container) !important;
}
.on-error-container {
  color: var(--md-sys-color-error-container);
  background-color: var(--md-sys-color-on-error-container);
}
.success-container{
  background-color: rgb(56, 142, 60) !important;
  color: var(--fn-ref-palette-white) !important;
}
.warning-container{
  background-color: rgb(237, 108, 2) !important;
  color: var(--fn-ref-palette-white) !important;
}
.info-container{
  background-color: rgb(2, 136, 209) !important;
  color: var(--fn-ref-palette-white) !important;
}
