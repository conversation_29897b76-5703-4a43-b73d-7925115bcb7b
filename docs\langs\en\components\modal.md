---
title: Modal
lang: en
---

# Modal

The modal component provides a solid foundation for creating dialogs, popovers, lightboxes, or whatever else.

The components using `Modal`:
* [Popover](./popover.md)
* [Menu](./menu.md)

## Basic usage

<demo src="../../../example/modal/basic.vue" preview="[8, 9]" />

## Backdrop

There is a backdrop by default, set `backdrop=false` to disabled it.

<demo src="../../../example/modal/backdrop.vue" preview="[18-23]" />
