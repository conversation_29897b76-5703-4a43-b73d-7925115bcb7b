.fn-action-area {
  position: relative;
  height: 100%;
  width: 100%;
  transition: background-color 600ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;

  &:not(&--disabled) {
    cursor: pointer;
    @media (any-hover: hover) {
      &:hover {
        background-color: rgba(
          var(--fn-sys-color-switch-reverse-rgb) /
            var(--md-sys-state-hover-state-layer-opacity)
        );
      }
    }

    &:active,
    &:focus {
      background-color: rgba(
        var(--fn-sys-color-switch-reverse-rgb) /
          var(--md-sys-state-focus-state-layer-opacity)
      );
    }
  }
}
